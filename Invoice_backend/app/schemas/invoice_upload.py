"""
Pydantic models for invoice upload and processing system.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator
from bson import ObjectId

from app.schemas.bill import PyObjectId


class ProcessingStatus(str, Enum):
    """Enum for processing status values"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class FileUploadRequest(BaseModel):
    """Schema for file upload request metadata"""
    pass  # File will be handled via FastAPI's UploadFile


class FileUploadResponse(BaseModel):
    """Schema for file upload response"""
    success: bool = Field(..., description="Whether upload was successful")
    message: str = Field(..., description="Response message")
    job_id: Optional[str] = Field(None, description="Unique job ID for tracking")
    file_path: Optional[str] = Field(None, description="Stored file path")
    error: Optional[str] = Field(None, description="Error message if failed")


class LineItem(BaseModel):
    """Schema for invoice line item"""
    description: str = Field(..., description="Item description")
    quantity: Union[int, float] = Field(..., description="Item quantity")
    unit_price: float = Field(..., description="Unit price")
    total_price: float = Field(..., description="Total price for this line item")


class VendorMatch(BaseModel):
    """Schema for vendor matching information"""
    vendor_name: str = Field(..., description="Matched vendor name")
    existing_vendor_id: Optional[str] = Field(None, description="Existing vendor ID if found")
    needs_creation: bool = Field(..., description="Whether vendor needs to be created")
    confidence_score: float = Field(..., description="Confidence score for the match")


class AccountingHeadMatch(BaseModel):
    """Schema for accounting head matching information"""
    extracted_accounting_head: str = Field(..., description="Extracted accounting head")
    suggested_accounting_head: str = Field(..., description="Suggested accounting head")
    existing_head_id: Optional[str] = Field(None, description="Existing head ID if found")
    needs_creation: bool = Field(..., description="Whether head needs to be created")
    confidence_score: float = Field(..., description="Confidence score for the match")


class ExtractedInvoice(BaseModel):
    """Schema for individual extracted invoice data"""
    is_invoice: bool = Field(..., description="Whether this is an invoice")
    is_continuation: bool = Field(..., description="Whether this is a continuation page")
    invoice_id: str = Field(..., description="Invoice ID")
    invoice_date: Optional[str] = Field(None, description="Invoice date")
    due_date: Optional[str] = Field(None, description="Due date")
    accounting_head: Optional[str] = Field(None, description="Accounting head")
    vendor_name: Optional[str] = Field(None, description="Vendor name")
    vendor_address: Optional[str] = Field(None, description="Vendor address")
    customer_name: Optional[str] = Field(None, description="Customer name")
    customer_address: Optional[str] = Field(None, description="Customer address")
    total_amount: Optional[float] = Field(None, description="Total amount")
    tax_amount: Optional[float] = Field(None, description="Tax amount")
    currency: Optional[str] = Field(None, description="Currency")
    line_items: Optional[List[LineItem]] = Field(None, description="Line items")
    start_page: Optional[int] = Field(None, description="Starting page number")
    vendor_match: Optional[VendorMatch] = Field(None, description="Vendor matching info")
    accounting_head_match: Optional[AccountingHeadMatch] = Field(None, description="Accounting head matching info")


class InvoiceJobInDB(BaseModel):
    """Schema for invoice job stored in database"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    job_id: str = Field(..., description="Unique job ID")
    uploaded_file_path: str = Field(..., description="Path to uploaded file")
    original_filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    content_type: str = Field(..., description="MIME content type")
    processing_status: ProcessingStatus = Field(default=ProcessingStatus.PENDING, description="Processing status")
    uploaded_by: str = Field(..., description="User ID who uploaded the file")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Upload timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
    retry_count: int = Field(default=0, description="Number of retry attempts")
    external_api_response: Optional[List[Dict[str, Any]]] = Field(None, description="Raw response from external API")
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class InvoiceInDB(BaseModel):
    """Schema for individual invoice stored in database"""
    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    job_id: str = Field(..., description="Reference to the upload job")
    uploaded_file_path: str = Field(..., description="Path to source file")
    uploaded_by: str = Field(..., description="User ID who uploaded the file")
    source: Optional[str] = Field(default="uploaded", description="Source of invoice (uploaded/mail)")

    # Extracted invoice data
    is_invoice: bool = Field(..., description="Whether this is an invoice")
    is_continuation: bool = Field(..., description="Whether this is a continuation page")
    invoice_id: str = Field(..., description="Invoice ID")
    invoice_date: Optional[str] = Field(None, description="Invoice date")
    due_date: Optional[str] = Field(None, description="Due date")
    accounting_head: Optional[str] = Field(None, description="Accounting head")
    vendor_name: Optional[str] = Field(None, description="Vendor name")
    vendor_address: Optional[str] = Field(None, description="Vendor address")
    customer_name: Optional[str] = Field(None, description="Customer name")
    customer_address: Optional[str] = Field(None, description="Customer address")
    total_amount: Optional[float] = Field(None, description="Total amount")
    tax_amount: Optional[float] = Field(None, description="Tax amount")
    currency: Optional[str] = Field(None, description="Currency")
    line_items: Optional[List[Dict[str, Any]]] = Field(None, description="Line items")
    start_page: Optional[int] = Field(None, description="Starting page number")
    vendor_match: Optional[Dict[str, Any]] = Field(None, description="Vendor matching info")
    accounting_head_match: Optional[Dict[str, Any]] = Field(None, description="Accounting head matching info")
    
    # Metadata
    processing_status: ProcessingStatus = Field(default=ProcessingStatus.COMPLETED, description="Processing status")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    processed_at: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class JobStatusResponse(BaseModel):
    """Schema for job status response"""
    success: bool = Field(..., description="Whether request was successful")
    job_id: str = Field(..., description="Job ID")
    status: ProcessingStatus = Field(..., description="Current processing status")
    created_at: datetime = Field(..., description="Job creation timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing completion timestamp")
    results: Optional[List[ExtractedInvoice]] = Field(None, description="Extracted invoice data")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    file_info: Optional[Dict[str, Any]] = Field(None, description="Original file information")


class InvoiceListResponse(BaseModel):
    """Schema for paginated invoice list response"""
    success: bool = Field(..., description="Whether request was successful")
    invoices: List[Dict[str, Any]] = Field(..., description="List of invoices")
    total: int = Field(..., description="Total number of invoices")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")


class InvoiceResponse(BaseModel):
    """Schema for individual invoice response"""
    success: bool = Field(..., description="Whether request was successful")
    invoice: Optional[Dict[str, Any]] = Field(None, description="Invoice data")
    error: Optional[str] = Field(None, description="Error message if failed")
