"""
Invoice upload and processing endpoints.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_db
from app.core.auth import get_current_admin
from app.core.config import settings
from app.schemas.invoice_upload import (
    FileUploadResponse, JobStatusResponse, ProcessingStatus, ExtractedInvoice
)
from app.services.invoice_processing import InvoiceProcessingService, get_invoice_processing_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/upload", response_model=FileUploadResponse)
async def upload_invoice_document(
    file: UploadFile = File(..., description="Invoice document to upload (PDF or image)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db),
    processing_service: InvoiceProcessingService = Depends(get_invoice_processing_service)
):
    """
    Upload invoice document for processing.
    
    This endpoint accepts PDF or image files containing invoices and:
    1. Validates file type and size (max 10MB)
    2. Stores the file securely with UUID filename
    3. Creates a processing job in the database
    4. Starts background processing to extract invoice data
    5. Returns a job ID for status tracking
    
    Supported file types:
    - PDF (.pdf)
    - Images (.jpg, .jpeg, .png, .tiff, .bmp)
    
    Authentication: Requires valid JWT token (admin only for now)
    
    Args:
        file: Uploaded file (multipart/form-data)
        current_admin: Current authenticated admin from JWT token
        
    Returns:
        JSON response with job ID for tracking or error information
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Processing file upload from admin: {admin_email}, filename: {file.filename}")
        
        # Validate file is provided
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "success": False,
                    "message": "No file provided",
                    "error": "missing_file"
                }
            )
        
        # Read file content
        try:
            file_content = await file.read()
        except Exception as e:
            logger.error(f"Error reading uploaded file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "success": False,
                    "message": "Failed to read uploaded file",
                    "error": "file_read_error"
                }
            )
        
        # Validate file
        is_valid, content_type, error_message = await processing_service.validate_file(
            file_content, file.filename
        )
        
        if not is_valid:
            logger.warning(f"File validation failed for {file.filename}: {error_message}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "success": False,
                    "message": "File validation failed",
                    "error": error_message
                }
            )
        
        # Store file
        try:
            file_path, unique_filename = await processing_service.store_file(
                file_content, file.filename
            )
        except Exception as e:
            logger.error(f"Error storing file {file.filename}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "success": False,
                    "message": "Failed to store uploaded file",
                    "error": "file_storage_error"
                }
            )
        
        # Create processing job
        try:
            job_id = await processing_service.create_job(
                file_path=file_path,
                original_filename=file.filename,
                file_size=len(file_content),
                content_type=content_type,
                uploaded_by=admin_email
            )
        except Exception as e:
            logger.error(f"Error creating processing job: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "success": False,
                    "message": "Failed to create processing job",
                    "error": "job_creation_error"
                }
            )
        
        # Start background processing
        try:
            await processing_service.start_background_processing(job_id)
        except Exception as e:
            logger.error(f"Error starting background processing: {str(e)}")
            # Don't fail the request if background processing fails to start
            # The job is created and can be processed later
            logger.warning(f"Background processing failed to start for job {job_id}, but job is created")
        
        logger.info(f"Successfully uploaded file {file.filename} with job ID: {job_id}")
        
        return FileUploadResponse(
            success=True,
            message=f"File uploaded successfully. Processing started with job ID: {job_id}",
            job_id=job_id,
            file_path=file_path
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "message": "Internal server error during file upload",
                "error": "server_error"
            }
        )


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_processing_status(
    job_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db),
    processing_service: InvoiceProcessingService = Depends(get_invoice_processing_service)
):
    """
    Get processing status for an uploaded invoice document.
    
    This endpoint returns the current processing status and results for a job:
    - pending: Job is queued for processing
    - processing: Job is currently being processed
    - completed: Processing finished successfully with results
    - failed: Processing failed with error details
    
    Authentication: Requires valid JWT token (admin only for now)
    
    Args:
        job_id: Unique job ID returned from upload endpoint
        current_admin: Current authenticated admin from JWT token
        
    Returns:
        JSON response with job status, results, and metadata
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Getting status for job {job_id} requested by admin: {admin_email}")
        
        # Get job status
        job_data = await processing_service.get_job_status(job_id)
        
        if not job_data:
            logger.warning(f"Job not found: {job_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "success": False,
                    "message": "Job not found",
                    "error": f"No job found with ID: {job_id}"
                }
            )
        
        # Check if admin has access to this job (basic security check)
        if job_data.get("uploaded_by") != admin_email:
            logger.warning(f"Admin {admin_email} attempted to access job {job_id} uploaded by {job_data.get('uploaded_by')}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "success": False,
                    "message": "Access denied",
                    "error": "You don't have permission to access this job"
                }
            )
        
        # Prepare response
        processing_status = ProcessingStatus(job_data.get("processing_status", "pending"))
        
        # Convert invoices to ExtractedInvoice format if completed
        results = None
        if processing_status == ProcessingStatus.COMPLETED and job_data.get("invoices"):
            results = []
            for invoice in job_data["invoices"]:
                try:
                    extracted_invoice = ExtractedInvoice(
                        is_invoice=invoice.get("is_invoice", False),
                        is_continuation=invoice.get("is_continuation", False),
                        invoice_id=invoice.get("invoice_id", ""),
                        invoice_date=invoice.get("invoice_date"),
                        due_date=invoice.get("due_date"),
                        accounting_head=invoice.get("accounting_head"),
                        vendor_name=invoice.get("vendor_name"),
                        vendor_address=invoice.get("vendor_address"),
                        customer_name=invoice.get("customer_name"),
                        customer_address=invoice.get("customer_address"),
                        total_amount=invoice.get("total_amount"),
                        tax_amount=invoice.get("tax_amount"),
                        currency=invoice.get("currency"),
                        line_items=invoice.get("line_items"),
                        start_page=invoice.get("start_page"),
                        vendor_match=invoice.get("vendor_match"),
                        accounting_head_match=invoice.get("accounting_head_match")
                    )
                    results.append(extracted_invoice)
                except Exception as e:
                    logger.warning(f"Error converting invoice data: {str(e)}")
                    # Skip invalid invoice data
                    continue
        
        # Prepare file info
        file_info = {
            "original_filename": job_data.get("original_filename"),
            "file_size": job_data.get("file_size"),
            "content_type": job_data.get("content_type"),
            "file_path": job_data.get("uploaded_file_path")
        }
        
        logger.info(f"Returning status for job {job_id}: {processing_status.value}")
        
        return JobStatusResponse(
            success=True,
            job_id=job_id,
            status=processing_status,
            created_at=job_data.get("created_at"),
            processed_at=job_data.get("processed_at"),
            results=results,
            error_message=job_data.get("error_message"),
            file_info=file_info
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "message": "Internal server error getting job status",
                "error": "server_error"
            }
        )


@router.get("/list", response_model=Dict[str, Any])
async def get_invoices_list(
    skip: int = Query(0, ge=0, description="Number of invoices to skip for pagination"),
    limit: int = Query(10, ge=1, le=100, description="Number of invoices to return (max 100)"),
    vendor_name: Optional[str] = Query(None, description="Filter by vendor name"),
    category: Optional[str] = Query(None, description="Filter by accounting head/category"),
    status_filter: Optional[str] = Query(None, alias="status", description="Filter by processing status"),
    source: Optional[str] = Query(None, description="Filter by source (uploaded/mail)"),
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Get paginated list of processed invoices with filtering options.

    This endpoint returns a list of all processed invoices with the following features:
    - Pagination support (skip/limit)
    - Filtering by vendor name, category, status, and source
    - Formatted data matching the UI requirements

    Response format matches the UI table structure:
    - Invoice Number (invoice_id)
    - Vendor (vendor_name)
    - Invoice Date (invoice_date)
    - Due Date (due_date)
    - Category (accounting_head or suggested_accounting_head)
    - Added Date (created_at)
    - Amount (total_amount + currency)
    - Source (uploaded/mail)
    - Status (processing status)

    Authentication: Requires valid JWT token (admin only for now)

    Args:
        skip: Number of invoices to skip (for pagination)
        limit: Number of invoices to return (max 100)
        vendor_name: Optional filter by vendor name
        category: Optional filter by accounting head/category
        status_filter: Optional filter by processing status
        source: Optional filter by source
        current_admin: Current authenticated admin from JWT token

    Returns:
        JSON response with paginated invoice list and metadata
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Getting invoices list for admin: {admin_email}, skip={skip}, limit={limit}")

        # Build filter query
        filter_query = {}

        # Filter by vendor name (case-insensitive partial match)
        if vendor_name:
            filter_query["vendor_name"] = {"$regex": vendor_name, "$options": "i"}

        # Filter by category (accounting head)
        if category:
            filter_query["$or"] = [
                {"accounting_head": {"$regex": category, "$options": "i"}},
                {"accounting_head_match.suggested_accounting_head": {"$regex": category, "$options": "i"}}
            ]

        # Filter by processing status
        if status_filter:
            filter_query["processing_status"] = status_filter

        # Filter by source
        if source:
            filter_query["source"] = source.lower()

        # Get invoices collection
        invoices_collection = db[settings.INVOICES_COLLECTION]

        # Get total count for pagination
        total_count = await invoices_collection.count_documents(filter_query)

        # Get paginated invoices
        cursor = invoices_collection.find(filter_query).sort("created_at", -1).skip(skip).limit(limit)
        invoices = await cursor.to_list(length=limit)

        # Format invoices for UI
        formatted_invoices = []
        for invoice in invoices:
            # Determine category (accounting head)
            category_value = invoice.get("accounting_head")
            if not category_value and invoice.get("accounting_head_match"):
                category_value = invoice.get("accounting_head_match", {}).get("suggested_accounting_head")

            # Format amount with currency
            amount = invoice.get("total_amount", 0)
            currency = invoice.get("currency", "")
            amount_display = f"{amount} {currency}".strip() if currency else str(amount)

            # Determine source
            source_value = invoice.get("source", "uploaded").title()

            # Format status for display
            status_value = invoice.get("processing_status", "completed").title()

            formatted_invoice = {
                "id": str(invoice.get("_id")),
                "invoice_number": invoice.get("invoice_id", ""),
                "vendor": invoice.get("vendor_name", ""),
                "invoice_date": invoice.get("invoice_date", ""),
                "due_date": invoice.get("due_date", ""),
                "category": category_value or "",
                "added_date": invoice.get("created_at"),
                "amount": amount_display,
                "source": source_value,
                "status": status_value,
                "job_id": invoice.get("job_id", ""),
                "uploaded_by": invoice.get("uploaded_by", ""),
                "currency": currency,
                "total_amount": amount
            }
            formatted_invoices.append(formatted_invoice)

        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit  # Ceiling division
        current_page = (skip // limit) + 1

        logger.info(f"Returning {len(formatted_invoices)} invoices (total: {total_count})")

        return {
            "success": True,
            "invoices": formatted_invoices,
            "pagination": {
                "total": total_count,
                "page": current_page,
                "size": limit,
                "pages": total_pages,
                "has_next": skip + limit < total_count,
                "has_prev": skip > 0
            },
            "filters": {
                "vendor_name": vendor_name,
                "category": category,
                "status": status_filter,
                "source": source
            }
        }

    except Exception as e:
        logger.error(f"Unexpected error getting invoices list: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "message": "Internal server error getting invoices list",
                "error": "server_error"
            }
        )


@router.get("/details/{invoice_id}", response_model=Dict[str, Any])
async def get_invoice_details(
    invoice_id: str,
    current_admin: Dict[str, Any] = Depends(get_current_admin),
    db: AsyncIOMotorDatabase = Depends(get_db)
):
    """
    Get detailed information for a specific invoice.

    This endpoint returns complete invoice details including:
    - All extracted invoice data
    - Vendor matching information
    - Accounting head matching information
    - Processing metadata
    - File information

    Authentication: Requires valid JWT token (admin only for now)

    Args:
        invoice_id: The MongoDB ObjectId of the invoice
        current_admin: Current authenticated admin from JWT token

    Returns:
        JSON response with complete invoice details
    """
    try:
        admin_email = current_admin["admin_email"]
        logger.info(f"Getting invoice details for {invoice_id} requested by admin: {admin_email}")

        # Get invoices collection
        invoices_collection = db[settings.INVOICES_COLLECTION]

        # Find invoice by ID
        from bson import ObjectId
        try:
            invoice_object_id = ObjectId(invoice_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "success": False,
                    "message": "Invalid invoice ID format",
                    "error": "invalid_id"
                }
            )

        invoice = await invoices_collection.find_one({"_id": invoice_object_id})

        if not invoice:
            logger.warning(f"Invoice not found: {invoice_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "success": False,
                    "message": "Invoice not found",
                    "error": f"No invoice found with ID: {invoice_id}"
                }
            )

        # Convert ObjectId to string for JSON serialization
        invoice["_id"] = str(invoice["_id"])

        # Get job information if available
        job_info = None
        if invoice.get("job_id"):
            jobs_collection = db[settings.INVOICE_JOBS_COLLECTION]
            job = await jobs_collection.find_one({"job_id": invoice["job_id"]})
            if job:
                job["_id"] = str(job["_id"])
                job_info = {
                    "job_id": job.get("job_id"),
                    "original_filename": job.get("original_filename"),
                    "file_size": job.get("file_size"),
                    "content_type": job.get("content_type"),
                    "uploaded_file_path": job.get("uploaded_file_path"),
                    "created_at": job.get("created_at"),
                    "processed_at": job.get("processed_at")
                }

        # Format response
        response_data = {
            "success": True,
            "invoice": invoice,
            "job_info": job_info,
            "formatted": {
                "invoice_number": invoice.get("invoice_id", ""),
                "vendor": invoice.get("vendor_name", ""),
                "vendor_address": invoice.get("vendor_address", ""),
                "customer": invoice.get("customer_name", ""),
                "customer_address": invoice.get("customer_address", ""),
                "invoice_date": invoice.get("invoice_date", ""),
                "due_date": invoice.get("due_date", ""),
                "category": invoice.get("accounting_head") or (
                    invoice.get("accounting_head_match", {}).get("suggested_accounting_head", "")
                ),
                "amount": invoice.get("total_amount", 0),
                "currency": invoice.get("currency", ""),
                "tax_amount": invoice.get("tax_amount"),
                "line_items": invoice.get("line_items", []),
                "source": invoice.get("source", "uploaded").title(),
                "status": invoice.get("processing_status", "completed").title(),
                "added_date": invoice.get("created_at"),
                "processed_date": invoice.get("processed_at"),
                "uploaded_by": invoice.get("uploaded_by", "")
            }
        }

        logger.info(f"Returning invoice details for {invoice_id}")
        return response_data

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting invoice details for {invoice_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "message": "Internal server error getting invoice details",
                "error": "server_error"
            }
        )
