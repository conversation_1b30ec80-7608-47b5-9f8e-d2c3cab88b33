"""
Outlook email service for fetching and processing emails
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, UTC, timedelta

from app.schemas.outlook import (
    EmailFetchRequest, EmailFetchResponse, EmailMetadata, EmailContent,
    OutlookEmail, EmailAddress, EmailAttachmentInDB
)
from app.services.microsoft_graph_client import MicrosoftGraphClient
from app.services.outlook_database import OutlookDatabase
from app.services.file_storage_service import FileStorageService
from app.services.email_attachment_invoice_processor import get_email_attachment_invoice_processor

logger = logging.getLogger(__name__)


class OutlookEmailService:
    """Service for fetching and processing Outlook emails"""
    
    def __init__(self, database_ops: OutlookDatabase):
        """Initialize with database operations"""
        self.database_ops = database_ops
        self.graph_client = MicrosoftGraphClient()
        self.file_storage = FileStorageService()
        self.invoice_processor = get_email_attachment_invoice_processor()
    
    async def fetch_emails(
        self,
        connection_id: str,
        access_token: str,
        request: EmailFetchRequest
    ) -> EmailFetchResponse:
        """
        Fetch emails from Outlook with deduplication
        
        Args:
            connection_id: Outlook connection ID
            access_token: Valid access token
            request: Email fetch request parameters
            
        Returns:
            EmailFetchResponse with fetch results
        """
        try:
            # Build filter query for Microsoft Graph
            # Note: Microsoft Graph has limitations on complex filters
            # Removed date filtering to fetch all emails regardless of date
            # Removed hasAttachments filter due to "InefficientFilter" error - handled client-side instead
            filter_parts = []

            # Apply only simple filters that work reliably with Microsoft Graph
            if request.filter_unread:
                filter_parts.append("isRead eq false")

            filter_query = " and ".join(filter_parts) if filter_parts else None
            
            # Fetch emails from Microsoft Graph
            graph_result = await self.graph_client.fetch_emails(
                access_token=access_token,
                folder=request.folder,
                limit=request.limit,
                skip=request.skip,
                filter_query=filter_query
            )
            
            if not graph_result["success"]:
                logger.error(f"Failed to fetch emails from Graph API: {graph_result.get('error')}")
                return EmailFetchResponse(
                    success=False,
                    message=f"Failed to fetch emails: {graph_result.get('details', 'Unknown error')}",
                    total_emails=0,
                    new_emails=0,
                    emails=[],
                    has_more=False
                )
            
            raw_emails = graph_result["emails"]
            emails_fetched = len(raw_emails)
            emails_processed = 0
            emails_skipped = 0
            unique_emails = []
            
            # Process each email for deduplication
            for raw_email in raw_emails:
                try:
                    # Extract email metadata
                    email_metadata = await self._extract_email_metadata(raw_email)

                    # Apply client-side filters that couldn't be applied in Graph API
                    if not self._passes_client_side_filters(raw_email, request):
                        emails_skipped += 1
                        continue

                    # Check if email already processed
                    is_processed = await self.database_ops.is_email_processed(
                        connection_id, email_metadata.internet_message_id
                    )

                    if is_processed:
                        emails_skipped += 1
                        logger.debug(f"Skipping already processed email: {email_metadata.internet_message_id}")
                        continue
                    
                    # Add to unique emails list
                    unique_emails.append(email_metadata)
                    
                    # Process email with full content and attachments
                    enhanced_metadata = await self._process_email_with_content(
                        connection_id, access_token, raw_email, email_metadata
                    )

                    # Track email in database with enhanced content
                    await self.database_ops.track_email_with_content(connection_id, enhanced_metadata)
                    emails_processed += 1
                    
                except Exception as e:
                    logger.error(f"Error processing email: {str(e)}")
                    emails_skipped += 1
                    continue
            
            # Update last sync timestamp
            # Check if we're using AdminOutlookDatabase and use the appropriate method
            if hasattr(self.database_ops, 'update_last_sync_by_connection_id'):
                await self.database_ops.update_last_sync_by_connection_id(connection_id)
            else:
                await self.database_ops.update_last_sync(connection_id)
            
            logger.info(
                f"Email fetch completed - Fetched: {emails_fetched}, "
                f"Processed: {emails_processed}, Skipped: {emails_skipped}"
            )
            
            # Convert EmailMetadata to OutlookEmail format for response
            outlook_emails = []
            for email_meta in unique_emails:
                outlook_email = OutlookEmail(
                    id=email_meta.message_id,
                    internet_message_id=email_meta.internet_message_id,
                    subject=email_meta.subject,
                    body_preview=None,  # Not available in metadata
                    body_content=None,  # Not available in metadata
                    body_content_type="text",
                    from_address=EmailAddress(
                        name=email_meta.sender_name,
                        address=email_meta.sender_email or ""
                    ) if email_meta.sender_email else None,
                    to_recipients=[],  # Not available in metadata
                    cc_recipients=[],  # Not available in metadata
                    bcc_recipients=[],  # Not available in metadata
                    received_datetime=email_meta.received_datetime,
                    sent_datetime=None,  # Not available in metadata
                    has_attachments=email_meta.has_attachments,
                    attachments=[],  # Not available in metadata
                    importance=email_meta.importance,
                    is_read=email_meta.is_read,
                    web_link=None  # Not available in metadata
                )
                outlook_emails.append(outlook_email)

            return EmailFetchResponse(
                success=True,
                message=f"Successfully fetched {emails_processed} new emails",
                total_emails=emails_fetched,
                new_emails=emails_processed,
                emails=outlook_emails,
                has_more=graph_result.get("has_more", False),
                next_page_token=str(request.skip + request.limit) if graph_result.get("has_more", False) else None
            )
            
        except Exception as e:
            logger.error(f"Error in fetch_emails: {str(e)}")
            return EmailFetchResponse(
                success=False,
                message=f"Email fetch failed: {str(e)}",
                total_emails=0,
                new_emails=0,
                emails=[],
                has_more=False
            )
    
    async def fetch_emails_with_content(
        self,
        connection_id: str,
        access_token: str,
        request: EmailFetchRequest,
        include_attachments: bool = True
    ) -> Dict[str, Any]:
        """
        Fetch emails with full content and attachments
        
        Args:
            connection_id: Outlook connection ID
            access_token: Valid access token
            request: Email fetch request parameters
            include_attachments: Whether to fetch attachments
            
        Returns:
            Dictionary with emails and their content
        """
        try:
            # First fetch email metadata
            fetch_response = await self.fetch_emails(connection_id, access_token, request)
            
            if not fetch_response.success:
                return {
                    "success": False,
                    "message": fetch_response.message,
                    "emails": []
                }
            
            # Get the processed emails from database
            processed_emails_result = await self.database_ops.get_processed_emails(
                connection_id, skip=request.skip, limit=request.limit
            )
            
            if not processed_emails_result["success"]:
                return {
                    "success": False,
                    "message": "Failed to retrieve processed emails",
                    "emails": []
                }
            
            emails_with_content = []
            
            # Fetch full content for each email
            for email_metadata in processed_emails_result["emails"]:
                try:
                    # Fetch email content from Graph API
                    email_content = await self._fetch_email_content(
                        access_token, email_metadata["message_id"], include_attachments
                    )
                    
                    if email_content:
                        emails_with_content.append({
                            "metadata": email_metadata,
                            "content": email_content
                        })
                    
                except Exception as e:
                    logger.error(f"Error fetching content for email {email_metadata['message_id']}: {str(e)}")
                    continue
            
            return {
                "success": True,
                "message": f"Successfully fetched {len(emails_with_content)} emails with content",
                "emails": emails_with_content,
                "total_fetched": fetch_response.total_emails,
                "total_processed": fetch_response.new_emails,
                "total_skipped": fetch_response.total_emails - fetch_response.new_emails,
                "has_more": fetch_response.has_more,
                "next_skip": fetch_response.next_page_token
            }
            
        except Exception as e:
            logger.error(f"Error fetching emails with content: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to fetch emails with content: {str(e)}",
                "emails": []
            }
    
    async def fetch_incremental_emails(
        self,
        connection_id: str,
        access_token: str,
        delta_link: Optional[str] = None,
        folder: str = "inbox"
    ) -> Dict[str, Any]:
        """
        Fetch emails using delta queries for incremental sync
        
        Args:
            connection_id: Outlook connection ID
            access_token: Valid access token
            delta_link: Previous delta link for incremental fetch
            folder: Email folder to sync
            
        Returns:
            Dictionary with delta sync results
        """
        try:
            # Fetch delta emails from Graph API
            delta_result = await self.graph_client.get_delta_emails(
                access_token=access_token,
                delta_link=delta_link,
                folder=folder
            )
            
            if not delta_result["success"]:
                logger.error(f"Failed to fetch delta emails: {delta_result.get('error')}")
                return {
                    "success": False,
                    "message": f"Delta sync failed: {delta_result.get('details', 'Unknown error')}",
                    "emails": [],
                    "delta_link": delta_link
                }
            
            raw_emails = delta_result["emails"]
            emails_processed = 0
            emails_skipped = 0
            new_emails = []
            
            # Process delta emails
            for raw_email in raw_emails:
                try:
                    # Check if this is a deletion (delta queries can include deletions)
                    if raw_email.get("@removed"):
                        logger.debug(f"Email deleted: {raw_email.get('id')}")
                        continue
                    
                    # Extract email metadata
                    email_metadata = await self._extract_email_metadata(raw_email)
                    
                    # Check if email already processed
                    is_processed = await self.database_ops.is_email_processed(
                        connection_id, email_metadata.internet_message_id
                    )
                    
                    if is_processed:
                        emails_skipped += 1
                        continue
                    
                    # Add to new emails list
                    new_emails.append(email_metadata)
                    
                    # Track email in database
                    await self.database_ops.track_email(connection_id, email_metadata)
                    emails_processed += 1
                    
                except Exception as e:
                    logger.error(f"Error processing delta email: {str(e)}")
                    emails_skipped += 1
                    continue
            
            # Update last sync timestamp
            # Check if we're using AdminOutlookDatabase and use the appropriate method
            if hasattr(self.database_ops, 'update_last_sync_by_connection_id'):
                await self.database_ops.update_last_sync_by_connection_id(connection_id)
            else:
                await self.database_ops.update_last_sync(connection_id)
            
            logger.info(
                f"Delta sync completed - New emails: {emails_processed}, "
                f"Skipped: {emails_skipped}, Is initial: {delta_result.get('is_initial_sync', False)}"
            )
            
            return {
                "success": True,
                "message": f"Delta sync completed - {emails_processed} new emails",
                "emails": new_emails,
                "emails_processed": emails_processed,
                "emails_skipped": emails_skipped,
                "delta_link": delta_result.get("delta_link"),
                "is_initial_sync": delta_result.get("is_initial_sync", False)
            }
            
        except Exception as e:
            logger.error(f"Error in incremental email fetch: {str(e)}")
            return {
                "success": False,
                "message": f"Delta sync failed: {str(e)}",
                "emails": [],
                "delta_link": delta_link
            }
    
    async def _extract_email_metadata(self, raw_email: Dict[str, Any]) -> EmailMetadata:
        """
        Extract email metadata from Graph API response
        
        Args:
            raw_email: Raw email data from Graph API
            
        Returns:
            EmailMetadata object
        """
        # Extract sender information
        sender_email = ""
        sender_name = None
        
        if "sender" in raw_email and raw_email["sender"]:
            sender_info = raw_email["sender"].get("emailAddress", {})
            sender_email = sender_info.get("address", "")
            sender_name = sender_info.get("name")
        elif "from" in raw_email and raw_email["from"]:
            from_info = raw_email["from"].get("emailAddress", {})
            sender_email = from_info.get("address", "")
            sender_name = from_info.get("name")
        
        # Parse received datetime
        received_datetime_str = raw_email.get("receivedDateTime", "")
        try:
            received_datetime = datetime.fromisoformat(
                received_datetime_str.replace("Z", "+00:00")
            )
        except (ValueError, AttributeError):
            received_datetime = datetime.now(UTC)
        
        return EmailMetadata(
            message_id=raw_email.get("id", ""),
            internet_message_id=raw_email.get("internetMessageId", ""),
            subject=raw_email.get("subject", ""),
            sender_email=sender_email,
            sender_name=sender_name,
            received_datetime=received_datetime,
            has_attachments=raw_email.get("hasAttachments", False),
            importance=raw_email.get("importance", "normal").lower(),
            is_read=raw_email.get("isRead", False),
            folder_name=raw_email.get("parentFolderId") or "inbox"  # Default to "inbox" if None
        )
    
    async def _fetch_email_content(
        self,
        access_token: str,
        message_id: str,
        include_attachments: bool = True
    ) -> Optional[EmailContent]:
        """
        Fetch full email content including body and attachments
        
        Args:
            access_token: Valid access token
            message_id: Email message ID
            include_attachments: Whether to fetch attachments
            
        Returns:
            EmailContent object or None if failed
        """
        try:
            # Fetch email details from Graph API
            import httpx
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # Get email with body
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"https://graph.microsoft.com/v1.0/me/messages/{message_id}",
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to fetch email content: {response.status_code}")
                    return None
                
                email_data = response.json()
            
            # Extract email content
            body_content = ""
            if "body" in email_data:
                body_content = email_data["body"].get("content", "")
            
            # Extract sender information
            sender_email = ""
            sender_name = None
            
            if "sender" in email_data and email_data["sender"]:
                sender_info = email_data["sender"].get("emailAddress", {})
                sender_email = sender_info.get("address", "")
                sender_name = sender_info.get("name")
            
            # Parse received datetime
            received_datetime_str = email_data.get("receivedDateTime", "")
            try:
                received_datetime = datetime.fromisoformat(
                    received_datetime_str.replace("Z", "+00:00")
                )
            except (ValueError, AttributeError):
                received_datetime = datetime.now(UTC)
            
            # Fetch attachments if requested
            attachments = []
            if include_attachments and email_data.get("hasAttachments", False):
                attachment_result = await self.graph_client.fetch_email_attachments(
                    access_token, message_id
                )
                if attachment_result["success"]:
                    attachments = attachment_result["attachments"]
            
            return EmailContent(
                subject=email_data.get("subject", ""),
                body=body_content,
                sender_email=sender_email,
                sender_name=sender_name,
                received_datetime=received_datetime,
                attachments=attachments
            )
            
        except Exception as e:
            logger.error(f"Error fetching email content for {message_id}: {str(e)}")
            return None

    def _passes_client_side_filters(self, email_data: Dict[str, Any], request: EmailFetchRequest) -> bool:
        """
        Apply client-side filters that couldn't be applied in Graph API query

        Args:
            email_data: Raw email data from Graph API
            request: Original fetch request with filter criteria

        Returns:
            True if email passes all filters, False otherwise
        """
        try:
            # Filter by read status if not already applied in Graph API
            if request.filter_unread is not None:
                is_read = email_data.get("isRead", False)
                if request.filter_unread and is_read:
                    return False
                if not request.filter_unread and not is_read:
                    return False

            # Filter by attachments if not already applied in Graph API
            if request.filter_has_attachments is not None:
                has_attachments = email_data.get("hasAttachments", False)
                if request.filter_has_attachments != has_attachments:
                    return False

            # Removed date filtering - now fetching all emails regardless of date

            return True

        except Exception as e:
            logger.error(f"Error in client-side filtering: {str(e)}")
            # If there's an error in filtering, let the email pass through
            return True

    async def get_email_statistics(self, connection_id: str) -> Dict[str, Any]:
        """
        Get email processing statistics for a connection
        
        Args:
            connection_id: Connection ID
            
        Returns:
            Dictionary with email statistics
        """
        try:
            # Get processed emails from database
            result = await self.database_ops.get_processed_emails(
                connection_id, skip=0, limit=1
            )
            
            if not result["success"]:
                return {
                    "success": False,
                    "message": "Failed to get email statistics"
                }
            
            total_emails = result["total"]
            
            # Get connection info for last sync
            connection = await self.database_ops.get_connection(connection_id)
            last_sync = connection.last_sync if connection else None
            
            return {
                "success": True,
                "total_emails_processed": total_emails,
                "last_sync": last_sync,
                "connection_active": connection.is_active if connection else False
            }
            
        except Exception as e:
            logger.error(f"Error getting email statistics: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to get statistics: {str(e)}"
            }

    async def _process_email_with_content(
        self,
        connection_id: str,
        access_token: str,
        raw_email: Dict[str, Any],
        base_metadata: EmailMetadata
    ) -> Dict[str, Any]:
        """
        Process email with full content and attachments

        Args:
            connection_id: Connection ID
            access_token: Valid access token
            raw_email: Raw email data from Graph API
            base_metadata: Basic email metadata

        Returns:
            Enhanced email metadata with content and attachments
        """
        try:
            # Start with base metadata
            enhanced_data = {
                "message_id": base_metadata.message_id,
                "internet_message_id": base_metadata.internet_message_id,
                "subject": base_metadata.subject,
                "sender_email": base_metadata.sender_email,
                "sender_name": base_metadata.sender_name,
                "received_datetime": base_metadata.received_datetime,
                "has_attachments": base_metadata.has_attachments,
                "importance": base_metadata.importance,
                "is_read": base_metadata.is_read,
                "folder_name": base_metadata.folder_name,
                "processing_status": "processed",
                "processed_at": datetime.now(UTC),
                "content_downloaded": False,
                "attachments_downloaded": False,
                "attachments": [],
                "attachment_count": 0
            }

            # Extract email content from raw data
            body = raw_email.get("body", {})
            if body:
                enhanced_data["body_content"] = body.get("content", "")
                enhanced_data["body_content_type"] = body.get("contentType", "text").lower()
                enhanced_data["content_downloaded"] = True

            # Get body preview
            enhanced_data["body_preview"] = raw_email.get("bodyPreview", "")

            # Process attachments if present
            if base_metadata.has_attachments:
                attachments_result = await self._download_email_attachments(
                    connection_id, access_token, base_metadata.message_id
                )

                if attachments_result["success"]:
                    enhanced_data["attachments"] = attachments_result["attachments"]
                    enhanced_data["attachment_count"] = len(attachments_result["attachments"])
                    enhanced_data["attachments_downloaded"] = True
                    logger.info(f"Downloaded {enhanced_data['attachment_count']} attachments for email {base_metadata.message_id}")
                else:
                    logger.warning(f"Failed to download attachments for email {base_metadata.message_id}: {attachments_result.get('error')}")

            return enhanced_data

        except Exception as e:
            logger.error(f"Error processing email with content: {str(e)}")
            # Return basic metadata if content processing fails
            return {
                "message_id": base_metadata.message_id,
                "internet_message_id": base_metadata.internet_message_id,
                "subject": base_metadata.subject,
                "sender_email": base_metadata.sender_email,
                "sender_name": base_metadata.sender_name,
                "received_datetime": base_metadata.received_datetime,
                "has_attachments": base_metadata.has_attachments,
                "importance": base_metadata.importance,
                "is_read": base_metadata.is_read,
                "folder_name": base_metadata.folder_name,
                "processing_status": "processed",
                "processed_at": datetime.now(UTC),
                "content_downloaded": False,
                "attachments_downloaded": False,
                "attachments": [],
                "attachment_count": 0
            }

    async def _download_email_attachments(
        self,
        connection_id: str,
        access_token: str,
        message_id: str
    ) -> Dict[str, Any]:
        """
        Download all attachments for an email with duplicate prevention

        Args:
            connection_id: Connection ID
            access_token: Valid access token
            message_id: Email message ID

        Returns:
            Dictionary with attachment download results
        """
        try:
            # First check if attachments have already been downloaded for this email
            existing_attachments = await self._get_existing_email_attachments(connection_id, message_id)
            if existing_attachments:
                logger.info(f"Attachments already downloaded for email {message_id}, skipping download")

                # Still process for invoice extraction if not already processed
                invoice_processing_result = None
                try:
                    logger.info(f"Processing existing {len(existing_attachments)} attachments for invoice extraction")
                    invoice_processing_result = await self.invoice_processor.process_email_attachments(
                        connection_id=connection_id,
                        email_id=message_id,
                        attachments=existing_attachments,
                        user_email="system"
                    )
                    logger.info(f"Invoice processing completed: {invoice_processing_result.get('processed_count', 0)} invoices processed")
                except Exception as e:
                    # Log error but don't fail the email processing
                    logger.error(f"Error during invoice processing for existing attachments {message_id}: {str(e)}")
                    invoice_processing_result = {
                        "success": False,
                        "error": str(e),
                        "processed_count": 0
                    }

                return {
                    "success": True,
                    "attachments": existing_attachments,
                    "total_downloaded": len(existing_attachments),
                    "already_existed": True,
                    "invoice_processing": invoice_processing_result
                }

            # Get attachments list from Microsoft Graph
            attachments_result = await self.graph_client.get_email_attachments(
                access_token, message_id
            )

            if not attachments_result["success"]:
                return {
                    "success": False,
                    "error": attachments_result.get("error", "Failed to get attachments list"),
                    "attachments": []
                }

            downloaded_attachments = []

            for attachment in attachments_result["attachments"]:
                attachment_id = attachment.get("id")
                attachment_name = attachment.get("name", "unknown_attachment")

                if not attachment_id:
                    logger.warning(f"Skipping attachment without ID: {attachment_name}")
                    continue

                # Download attachment content
                download_result = await self.graph_client.download_attachment(
                    access_token, message_id, attachment_id
                )

                if download_result["success"]:
                    # Save attachment to file system
                    save_result = await self.file_storage.save_attachment(
                        connection_id=connection_id,
                        email_id=message_id,
                        attachment_name=download_result["name"],
                        content=download_result["content"],
                        content_type=download_result["content_type"]
                    )

                    if save_result["success"]:
                        # Create attachment record for database
                        attachment_record = {
                            "name": download_result["name"],
                            "content_type": download_result["content_type"],
                            "size": download_result["size"],
                            "file_path": save_result["file_path"],
                            "download_url": None,  # Could store Graph API URL if needed
                            "downloaded_at": datetime.now(UTC),
                            "already_existed": save_result.get("already_existed", False)
                        }
                        downloaded_attachments.append(attachment_record)

                        if save_result.get("already_existed", False):
                            logger.info(f"Attachment already existed: {download_result['name']}")
                        else:
                            logger.info(f"Successfully downloaded attachment: {download_result['name']}")
                    else:
                        logger.error(f"Failed to save attachment {download_result['name']}: {save_result.get('error')}")
                else:
                    logger.error(f"Failed to download attachment {attachment_name}: {download_result.get('error')}")

            # Process attachments for invoice extraction (if any were downloaded)
            invoice_processing_result = None
            if downloaded_attachments:
                try:
                    logger.info(f"Processing {len(downloaded_attachments)} attachments for invoice extraction")
                    invoice_processing_result = await self.invoice_processor.process_email_attachments(
                        connection_id=connection_id,
                        email_id=message_id,
                        attachments=downloaded_attachments,
                        user_email="system"  # System user for email-based processing
                    )
                    logger.info(f"Invoice processing completed: {invoice_processing_result.get('processed_count', 0)} invoices processed")
                except Exception as e:
                    # Log error but don't fail the email processing
                    logger.error(f"Error during invoice processing for email {message_id}: {str(e)}")
                    invoice_processing_result = {
                        "success": False,
                        "error": str(e),
                        "processed_count": 0
                    }

            return {
                "success": True,
                "attachments": downloaded_attachments,
                "total_downloaded": len(downloaded_attachments),
                "invoice_processing": invoice_processing_result
            }

        except Exception as e:
            logger.error(f"Error downloading email attachments: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "attachments": []
            }

    async def _get_existing_email_attachments(
        self,
        connection_id: str,
        message_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Check if attachments have already been downloaded for this email

        Args:
            connection_id: Connection ID
            message_id: Email message ID

        Returns:
            List of existing attachment records if found, None otherwise
        """
        try:
            # Check if we're using AdminOutlookDatabase
            if hasattr(self.database_ops, 'admin_collection'):
                # For admin database, check in admin collection
                admin_doc = await self.database_ops.admin_collection.find_one({
                    "outlook_connections.emails.message_id": message_id,
                    "outlook_connections.emails.attachments_downloaded": True
                })

                if admin_doc:
                    # Find the specific email and return its attachments
                    for connection in admin_doc.get("outlook_connections", []):
                        for email in connection.get("emails", []):
                            if email.get("message_id") == message_id and email.get("attachments_downloaded", False):
                                attachments = email.get("attachments", [])
                                if attachments:
                                    logger.debug(f"Found {len(attachments)} existing attachments for email {message_id}")
                                    return attachments
            else:
                # For regular database, check in email_tracking collection
                from bson import ObjectId
                email_record = await self.database_ops.email_tracking_collection.find_one({
                    "connection_id": ObjectId(connection_id),
                    "message_id": message_id,
                    "attachments_downloaded": True
                })

                if email_record and email_record.get("attachments"):
                    attachments = email_record.get("attachments", [])
                    logger.debug(f"Found {len(attachments)} existing attachments for email {message_id}")
                    return attachments

            return None

        except Exception as e:
            logger.error(f"Error checking existing attachments for email {message_id}: {str(e)}")
            return None