"""
Invoice processing service for handling document upload, validation, and external API integration.
"""

import asyncio
import logging
import uuid
import aiofiles
import aiohttp
import magic
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, UTC
from concurrent.futures import Thread<PERSON>oolExecutor
from motor.motor_asyncio import AsyncIOMotorDatabase
from fastapi import Depends

from app.core.config import settings
from app.core.database import get_db
from app.schemas.invoice_upload import (
    ProcessingStatus, InvoiceJobInDB, InvoiceInDB, ExtractedInvoice
)

logger = logging.getLogger(__name__)

# File validation constants
ALLOWED_EXTENSIONS = {'.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp'}
ALLOWED_MIME_TYPES = {
    'application/pdf',
    'image/jpeg',
    'image/png', 
    'image/tiff',
    'image/bmp'
}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
EXTERNAL_API_URL = "http://127.0.0.1:8080/invoices/extract/"
EXTERNAL_API_TIMEOUT = 600  # 10 minutes
MAX_RETRIES = 3
RETRY_DELAYS = [1, 2, 4]  # Exponential backoff delays in seconds


class InvoiceProcessingService:
    """Service for processing invoice uploads and extracting data"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """Initialize with database connection"""
        self.db = database
        self.invoice_jobs_collection = self.db[settings.INVOICE_JOBS_COLLECTION]
        self.invoices_collection = self.db[settings.INVOICES_COLLECTION]
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def validate_file(self, file_content: bytes, filename: str) -> Tuple[bool, str, str]:
        """
        Validate uploaded file for type and size.
        
        Args:
            file_content: File content bytes
            filename: Original filename
            
        Returns:
            Tuple of (is_valid, content_type, error_message)
        """
        try:
            # Check file size
            if len(file_content) > MAX_FILE_SIZE:
                return False, "", f"File size {len(file_content)} bytes exceeds maximum allowed size of {MAX_FILE_SIZE} bytes"
            
            # Check file extension
            file_path = Path(filename)
            extension = file_path.suffix.lower()
            if extension not in ALLOWED_EXTENSIONS:
                return False, "", f"File extension '{extension}' not allowed. Allowed extensions: {', '.join(ALLOWED_EXTENSIONS)}"
            
            # Check MIME type using python-magic
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in ALLOWED_MIME_TYPES:
                return False, "", f"File MIME type '{mime_type}' not allowed. Allowed types: {', '.join(ALLOWED_MIME_TYPES)}"
            
            # Basic file header validation
            if not self._validate_file_header(file_content, mime_type):
                return False, "", "File appears to be corrupted or invalid"
            
            logger.info(f"File validation successful: {filename}, MIME: {mime_type}, Size: {len(file_content)} bytes")
            return True, mime_type, ""
            
        except Exception as e:
            logger.error(f"Error validating file {filename}: {str(e)}")
            return False, "", f"File validation error: {str(e)}"
    
    def _validate_file_header(self, file_content: bytes, mime_type: str) -> bool:
        """
        Validate file header to ensure file integrity.
        
        Args:
            file_content: File content bytes
            mime_type: MIME type
            
        Returns:
            True if header is valid, False otherwise
        """
        if len(file_content) < 4:
            return False
        
        # Check common file signatures
        header = file_content[:4]
        
        if mime_type == 'application/pdf':
            return header.startswith(b'%PDF')
        elif mime_type == 'image/jpeg':
            return header.startswith(b'\xff\xd8\xff')
        elif mime_type == 'image/png':
            return header.startswith(b'\x89PNG')
        elif mime_type == 'image/bmp':
            return header.startswith(b'BM')
        elif mime_type == 'image/tiff':
            return header.startswith(b'II*\x00') or header.startswith(b'MM\x00*')
        
        return True  # Default to valid for other types

    def _generate_public_url(self, filename: str) -> str:
        """
        Generate a publicly accessible URL for the uploaded file.

        Args:
            filename: The unique filename

        Returns:
            Public URL for the file
        """
        # Use localhost for external access even if server binds to 0.0.0.0
        host = "localhost" if settings.HOST == "0.0.0.0" else settings.HOST
        base_url = f"http://{host}:{settings.PORT}"
        return f"{base_url}{settings.API_V1_STR}/files/invoices/{filename}"

    async def store_file(self, file_content: bytes, original_filename: str) -> Tuple[str, str]:
        """
        Store uploaded file with UUID filename and return public URL.

        Args:
            file_content: File content bytes
            original_filename: Original filename

        Returns:
            Tuple of (public_url, unique_filename)
        """
        try:
            # Generate unique filename
            file_extension = Path(original_filename).suffix.lower()
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # Create storage path
            storage_dir = Path(settings.PROJECT_ROOT) / "storage" / "invoices"
            storage_dir.mkdir(parents=True, exist_ok=True)

            file_path = storage_dir / unique_filename

            # Store file asynchronously
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)

            # Generate public URL instead of local path
            public_url = self._generate_public_url(unique_filename)

            logger.info(f"File stored successfully at: {file_path}")
            logger.info(f"Public URL generated: {public_url}")
            return public_url, unique_filename

        except Exception as e:
            logger.error(f"Error storing file {original_filename}: {str(e)}")
            raise
    
    async def create_job(
        self, 
        file_path: str, 
        original_filename: str, 
        file_size: int, 
        content_type: str, 
        uploaded_by: str
    ) -> str:
        """
        Create a new processing job in the database.

        Args:
            file_path: Public URL to the stored file
            original_filename: Original filename
            file_size: File size in bytes
            content_type: MIME content type
            uploaded_by: User ID who uploaded the file

        Returns:
            Job ID
        """
        try:
            job_id = str(uuid.uuid4())
            
            job_data = InvoiceJobInDB(
                job_id=job_id,
                uploaded_file_path=file_path,
                original_filename=original_filename,
                file_size=file_size,
                content_type=content_type,
                processing_status=ProcessingStatus.PENDING,
                uploaded_by=uploaded_by,
                created_at=datetime.now(UTC)
            )
            
            # Insert job into database
            result = await self.invoice_jobs_collection.insert_one(job_data.model_dump(by_alias=True))
            
            logger.info(f"Created processing job: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error creating job: {str(e)}")
            raise
    
    async def start_background_processing(self, job_id: str) -> None:
        """
        Start background processing for a job.
        
        Args:
            job_id: Job ID to process
        """
        # Start processing in background without blocking
        asyncio.create_task(self._process_job_async(job_id))
        logger.info(f"Started background processing for job: {job_id}")
    
    async def _process_job_async(self, job_id: str) -> None:
        """
        Process a job asynchronously with retry logic.
        
        Args:
            job_id: Job ID to process
        """
        try:
            # Update status to processing
            await self._update_job_status(job_id, ProcessingStatus.PROCESSING)
            
            # Get job details
            job = await self.invoice_jobs_collection.find_one({"job_id": job_id})
            if not job:
                logger.error(f"Job not found: {job_id}")
                return
            
            # Process with retry logic
            success = False
            last_error = None
            
            for attempt in range(MAX_RETRIES):
                try:
                    logger.info(f"Processing attempt {attempt + 1}/{MAX_RETRIES} for job: {job_id}")
                    
                    # Call external API
                    api_response = await self._call_external_api(job["uploaded_file_path"])
                    
                    # Store results
                    await self._store_extraction_results(job_id, job, api_response)
                    
                    # Update job status to completed (clear any previous error message)
                    await self._update_job_status(
                        job_id,
                        ProcessingStatus.COMPLETED,
                        error_message=None,  # Clear previous error message
                        external_api_response=api_response
                    )
                    
                    success = True
                    logger.info(f"Successfully processed job: {job_id}")
                    break
                    
                except Exception as e:
                    last_error = str(e)
                    logger.warning(f"Attempt {attempt + 1} failed for job {job_id}: {last_error}")
                    
                    if attempt < MAX_RETRIES - 1:
                        # Wait before retry
                        await asyncio.sleep(RETRY_DELAYS[attempt])
                    
                    # Update retry count
                    await self.invoice_jobs_collection.update_one(
                        {"job_id": job_id},
                        {"$inc": {"retry_count": 1}}
                    )
            
            if not success:
                # Mark job as failed
                await self._update_job_status(
                    job_id, 
                    ProcessingStatus.FAILED, 
                    error_message=f"Failed after {MAX_RETRIES} attempts. Last error: {last_error}"
                )
                logger.error(f"Job processing failed after all retries: {job_id}")
                
        except Exception as e:
            logger.error(f"Unexpected error processing job {job_id}: {str(e)}")
            await self._update_job_status(
                job_id,
                ProcessingStatus.FAILED,
                error_message=f"Unexpected error: {str(e)}"
            )

    async def _call_external_api(self, file_url: str) -> List[Dict[str, Any]]:
        """
        Call external invoice extraction API.

        Args:
            file_url: Public URL to the file to process

        Returns:
            API response data
        """
        try:
            # Extract filename from URL and construct local file path
            # URL format: http://localhost:8000/api/v1/files/invoices/{filename}
            if file_url.startswith('http'):
                # Extract filename from URL
                filename = file_url.split('/')[-1]
            else:
                # Handle legacy file paths (for backward compatibility)
                filename = Path(file_url).name

            absolute_path = Path(settings.PROJECT_ROOT) / "storage" / "invoices" / filename

            if not absolute_path.exists():
                raise FileNotFoundError(f"File not found: {absolute_path}")

            # Determine content type from file extension
            file_extension = absolute_path.suffix.lower()
            content_type_map = {
                '.pdf': 'application/pdf',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.tiff': 'image/tiff',
                '.tif': 'image/tiff',
                '.bmp': 'image/bmp'
            }
            content_type = content_type_map.get(file_extension, 'application/octet-stream')

            # Prepare multipart form data
            async with aiofiles.open(absolute_path, 'rb') as f:
                file_content = await f.read()

            # Create form data with proper content type
            data = aiohttp.FormData()
            data.add_field(
                'file',
                file_content,
                filename=absolute_path.name,
                content_type=content_type
            )

            # Log detailed information for debugging
            logger.info(f"Processing file from URL: {file_url}")
            logger.info(f"Preparing to send file: {absolute_path.name}")
            logger.info(f"File size: {len(file_content)} bytes")
            logger.info(f"Content type: {content_type}")
            logger.info(f"File extension: {file_extension}")
            logger.info(f"Local file path: {absolute_path}")

            # Call external API
            timeout = aiohttp.ClientTimeout(total=EXTERNAL_API_TIMEOUT)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                logger.info(f"Calling external API: {EXTERNAL_API_URL}")
                logger.info(f"File details: name={absolute_path.name}, size={len(file_content)} bytes, content_type={content_type}")

                async with session.post(EXTERNAL_API_URL, data=data) as response:
                    logger.info(f"External API response status: {response.status}")

                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"External API call successful, received {len(result)} invoices")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"External API error {response.status}: {error_text}")
                        logger.error(f"Request headers: {dict(response.request_info.headers) if hasattr(response, 'request_info') else 'N/A'}")
                        raise Exception(f"External API error {response.status}: {error_text}")

        except asyncio.TimeoutError:
            raise Exception(f"External API call timed out after {EXTERNAL_API_TIMEOUT} seconds")
        except Exception as e:
            logger.error(f"Error calling external API: {str(e)}")
            raise

    async def _store_extraction_results(
        self,
        job_id: str,
        job_data: Dict[str, Any],
        api_response: List[Dict[str, Any]],
        source: str = "uploaded"
    ) -> None:
        """
        Store extraction results in the invoices collection.

        Args:
            job_id: Job ID
            job_data: Job data from database
            api_response: Response from external API
            source: Source of the invoice (uploaded/mail)
        """
        try:
            invoices_to_insert = []

            for invoice_data in api_response:
                invoice_doc = InvoiceInDB(
                    job_id=job_id,
                    uploaded_file_path=job_data["uploaded_file_path"],
                    uploaded_by=job_data["uploaded_by"],
                    source=source,

                    # Extract invoice data
                    is_invoice=invoice_data.get("is_invoice", False),
                    is_continuation=invoice_data.get("is_continuation", False),
                    invoice_id=invoice_data.get("invoice_id", ""),
                    invoice_date=invoice_data.get("invoice_date"),
                    due_date=invoice_data.get("due_date"),
                    accounting_head=invoice_data.get("accounting_head"),
                    vendor_name=invoice_data.get("vendor_name"),
                    vendor_address=invoice_data.get("vendor_address"),
                    customer_name=invoice_data.get("customer_name"),
                    customer_address=invoice_data.get("customer_address"),
                    total_amount=invoice_data.get("total_amount"),
                    tax_amount=invoice_data.get("tax_amount"),
                    currency=invoice_data.get("currency"),
                    line_items=invoice_data.get("line_items"),
                    start_page=invoice_data.get("start_page"),
                    vendor_match=invoice_data.get("vendor_match"),
                    accounting_head_match=invoice_data.get("accounting_head_match"),

                    processing_status=ProcessingStatus.COMPLETED,
                    created_at=datetime.now(UTC),
                    processed_at=datetime.now(UTC)
                )

                invoices_to_insert.append(invoice_doc.model_dump(by_alias=True))

            # Insert all invoices
            if invoices_to_insert:
                await self.invoices_collection.insert_many(invoices_to_insert)
                logger.info(f"Stored {len(invoices_to_insert)} invoices for job: {job_id}")

        except Exception as e:
            logger.error(f"Error storing extraction results for job {job_id}: {str(e)}")
            raise

    async def _update_job_status(
        self,
        job_id: str,
        status: ProcessingStatus,
        error_message: Optional[str] = None,
        external_api_response: Optional[List[Dict[str, Any]]] = None
    ) -> None:
        """
        Update job status in database.

        Args:
            job_id: Job ID
            status: New status
            error_message: Error message if failed
            external_api_response: Raw API response data
        """
        try:
            update_data = {
                "processing_status": status.value,
                "updated_at": datetime.now(UTC)
            }

            if status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]:
                update_data["processed_at"] = datetime.now(UTC)

            # Handle error message (including clearing it when explicitly set to None)
            if error_message is not None:
                update_data["error_message"] = error_message
            elif status == ProcessingStatus.COMPLETED:
                # Clear error message when job completes successfully
                update_data["error_message"] = None

            if external_api_response:
                update_data["external_api_response"] = external_api_response

            await self.invoice_jobs_collection.update_one(
                {"job_id": job_id},
                {"$set": update_data}
            )

            logger.info(f"Updated job {job_id} status to: {status.value}")

        except Exception as e:
            logger.error(f"Error updating job status for {job_id}: {str(e)}")
            raise

    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get job status and results.

        Args:
            job_id: Job ID

        Returns:
            Job data or None if not found
        """
        try:
            job = await self.invoice_jobs_collection.find_one({"job_id": job_id})
            if not job:
                return None

            # Get associated invoices if completed
            invoices = []
            if job.get("processing_status") == ProcessingStatus.COMPLETED.value:
                cursor = self.invoices_collection.find({"job_id": job_id})
                invoices = await cursor.to_list(length=None)

                # Convert ObjectIds to strings for JSON serialization
                for invoice in invoices:
                    if "_id" in invoice:
                        invoice["_id"] = str(invoice["_id"])

            # Convert ObjectId to string
            if "_id" in job:
                job["_id"] = str(job["_id"])

            job["invoices"] = invoices
            return job

        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {str(e)}")
            raise


# Dependency function to get service instance
def get_invoice_processing_service(db: AsyncIOMotorDatabase = Depends(get_db)) -> InvoiceProcessingService:
    """Dependency to get invoice processing service instance"""
    return InvoiceProcessingService(db)
