"""
Email Attachment Invoice Processor Service

This service processes email attachments for invoice extraction.
It integrates with the existing email monitoring system to automatically
process PDF and image attachments through the external invoice extraction API.
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, UTC
from pathlib import Path

from app.services.attachment_service import AttachmentService
from app.services.invoice_processing import InvoiceProcessingService
from app.schemas.invoice_upload import InvoiceInDB, ProcessingStatus
from app.core.database import get_database
from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailAttachmentInvoiceProcessor:
    """Service for processing email attachments for invoice extraction"""
    
    def __init__(self, attachment_service: Optional[AttachmentService] = None):
        """
        Initialize the email attachment invoice processor
        
        Args:
            attachment_service: Optional AttachmentService instance
        """
        self.attachment_service = attachment_service
        self.database = None  # Will be initialized when needed
        self.invoices_collection = None
        self.external_api_url = "http://127.0.0.1:8080/invoices/extract/"
        logger.info("EmailAttachmentInvoiceProcessor initialized")
    
    async def _ensure_database_initialized(self):
        """Ensure database connection is initialized"""
        if self.database is None:
            self.database = await get_database()
            self.invoices_collection = self.database[settings.INVOICES_COLLECTION]
    
    async def process_email_attachments(
        self,
        connection_id: str,
        email_id: str,
        attachments: List[Dict[str, Any]],
        user_email: str = "system"
    ) -> Dict[str, Any]:
        """
        Process email attachments for invoice extraction
        
        Args:
            connection_id: Outlook connection ID
            email_id: Email message ID
            attachments: List of attachment metadata
            user_email: User email for tracking
            
        Returns:
            Dictionary with processing results
        """
        try:
            await self._ensure_database_initialized()
            
            logger.info(f"Processing {len(attachments)} attachments for email {email_id}")
            
            processed_count = 0
            skipped_count = 0
            failed_count = 0
            results = []
            
            for attachment in attachments:
                try:
                    # Check if attachment is a supported file type (PDF or image)
                    content_type = attachment.get("content_type", "")
                    if not self._is_supported_file_type(content_type):
                        logger.debug(f"Skipping unsupported file type: {attachment.get('name')} ({content_type})")
                        skipped_count += 1
                        continue
                    
                    # Process the attachment
                    result = await self._process_single_attachment(
                        connection_id, email_id, attachment, user_email
                    )
                    
                    if result["success"]:
                        processed_count += 1
                        results.append(result)
                        logger.info(f"Successfully processed attachment: {attachment.get('name')}")
                    else:
                        failed_count += 1
                        logger.warning(f"Failed to process attachment: {attachment.get('name')} - {result.get('error')}")
                        
                except Exception as e:
                    failed_count += 1
                    logger.error(f"Error processing attachment {attachment.get('name')}: {str(e)}")
                    continue
            
            logger.info(f"Attachment processing complete - Processed: {processed_count}, Skipped: {skipped_count}, Failed: {failed_count}")
            
            return {
                "success": True,
                "total_attachments": len(attachments),
                "processed_count": processed_count,
                "skipped_count": skipped_count,
                "failed_count": failed_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error in process_email_attachments: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "total_attachments": len(attachments),
                "processed_count": 0,
                "skipped_count": 0,
                "failed_count": len(attachments)
            }
    
    async def _process_single_attachment(
        self,
        connection_id: str,
        email_id: str,
        attachment: Dict[str, Any],
        user_email: str
    ) -> Dict[str, Any]:
        """
        Process a single attachment for invoice extraction
        
        Args:
            connection_id: Outlook connection ID
            email_id: Email message ID
            attachment: Attachment metadata
            user_email: User email for tracking
            
        Returns:
            Dictionary with processing result
        """
        try:
            attachment_name = attachment.get("name", "unknown")
            file_path = attachment.get("file_path", "")
            
            logger.debug(f"Processing attachment: {attachment_name} at {file_path}")
            
            # Get attachment content using AttachmentService
            if not self.attachment_service:
                from app.services.attachment_service import get_attachment_service
                self.attachment_service = get_attachment_service()
            
            content_result = await self.attachment_service.get_attachment_content(file_path)
            
            if not content_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to get attachment content: {content_result.get('error')}",
                    "attachment_name": attachment_name
                }
            
            # Call external API for invoice extraction
            api_result = await self._call_external_api(content_result)
            
            if not api_result["success"]:
                return {
                    "success": False,
                    "error": f"External API call failed: {api_result.get('error')}",
                    "attachment_name": attachment_name
                }
            
            # Store results in invoice collection
            storage_result = await self._store_invoice_results(
                connection_id, email_id, attachment, api_result["response"], user_email
            )
            
            if not storage_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to store results: {storage_result.get('error')}",
                    "attachment_name": attachment_name
                }
            
            return {
                "success": True,
                "attachment_name": attachment_name,
                "invoices_stored": storage_result["invoices_stored"]
            }
            
        except Exception as e:
            logger.error(f"Error processing single attachment: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "attachment_name": attachment.get("name", "unknown")
            }
    
    def _is_supported_file_type(self, content_type: str) -> bool:
        """
        Check if file type is supported for invoice processing (PDF or image)

        Args:
            content_type: MIME content type

        Returns:
            True if supported, False otherwise
        """
        return content_type.startswith('application/pdf') or content_type.startswith('image/')

    def _generate_attachment_public_url(self, file_path: str) -> str:
        """
        Generate a publicly accessible URL for the email attachment file.

        Args:
            file_path: The relative file path (e.g., "attachments/connection_id/email_id/filename.pdf")

        Returns:
            Public URL for the file
        """
        # Use localhost for external access even if server binds to 0.0.0.0
        host = "localhost" if settings.HOST == "0.0.0.0" else settings.HOST
        base_url = f"http://{host}:{settings.PORT}"
        # Convert file path to URL path (replace backslashes with forward slashes if needed)
        url_path = file_path.replace("\\", "/")
        return f"{base_url}{settings.API_V1_STR}/files/{url_path}"
    
    async def _call_external_api(self, content_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call external invoice extraction API
        
        Args:
            content_result: Result from attachment service containing file content
            
        Returns:
            Dictionary with API call result
        """
        try:
            import httpx
            
            filename = content_result["filename"]
            content = content_result["content"]
            content_type = content_result["content_type"]
            
            # Prepare file for multipart upload
            files = {
                "file": (filename, content, content_type)
            }
            
            logger.debug(f"Calling external API for file: {filename}")
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.external_api_url,
                    files=files
                )
            
            if response.status_code != 200:
                logger.error(f"External API returned error: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"API error: {response.status_code}",
                    "response_text": response.text
                }
            
            api_response = response.json()
            logger.debug(f"External API response received for {filename}")
            
            return {
                "success": True,
                "response": api_response
            }
            
        except Exception as e:
            logger.error(f"Error calling external API: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _store_invoice_results(
        self,
        connection_id: str,
        email_id: str,
        attachment: Dict[str, Any],
        api_response: List[Dict[str, Any]],
        user_email: str
    ) -> Dict[str, Any]:
        """
        Store invoice extraction results in the database
        
        Args:
            connection_id: Outlook connection ID
            email_id: Email message ID
            attachment: Attachment metadata
            api_response: Response from external API
            user_email: User email for tracking
            
        Returns:
            Dictionary with storage result
        """
        try:
            invoices_to_insert = []
            
            # Generate a unique job ID for this email attachment processing
            job_id = f"email_{connection_id}_{email_id}_{uuid.uuid4().hex[:8]}"

            # Generate public URL for the attachment file (same as manual upload)
            attachment_file_path = attachment.get("file_path", "")
            public_url = self._generate_attachment_public_url(attachment_file_path)

            for invoice_data in api_response:
                invoice_doc = InvoiceInDB(
                    job_id=job_id,
                    uploaded_file_path=public_url,  # Use public URL like manual uploads
                    uploaded_by=user_email,
                    source="mail",  # Mark as mail source
                    
                    # Extract invoice data
                    is_invoice=invoice_data.get("is_invoice", False),
                    is_continuation=invoice_data.get("is_continuation", False),
                    invoice_id=invoice_data.get("invoice_id", ""),
                    invoice_date=invoice_data.get("invoice_date"),
                    due_date=invoice_data.get("due_date"),
                    accounting_head=invoice_data.get("accounting_head"),
                    vendor_name=invoice_data.get("vendor_name"),
                    vendor_address=invoice_data.get("vendor_address"),
                    customer_name=invoice_data.get("customer_name"),
                    customer_address=invoice_data.get("customer_address"),
                    total_amount=invoice_data.get("total_amount"),
                    tax_amount=invoice_data.get("tax_amount"),
                    currency=invoice_data.get("currency"),
                    line_items=invoice_data.get("line_items"),
                    start_page=invoice_data.get("start_page"),
                    vendor_match=invoice_data.get("vendor_match"),
                    accounting_head_match=invoice_data.get("accounting_head_match"),
                    
                    processing_status=ProcessingStatus.COMPLETED,
                    created_at=datetime.now(UTC),
                    processed_at=datetime.now(UTC)
                )
                
                invoices_to_insert.append(invoice_doc.model_dump(by_alias=True))
            
            # Insert all invoices
            if invoices_to_insert:
                await self.invoices_collection.insert_many(invoices_to_insert)
                logger.info(f"Stored {len(invoices_to_insert)} invoices from email attachment")
            
            return {
                "success": True,
                "invoices_stored": len(invoices_to_insert)
            }
            
        except Exception as e:
            logger.error(f"Error storing invoice results: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# Dependency injection function
_email_attachment_processor_instance = None

def get_email_attachment_invoice_processor(
    attachment_service: Optional[AttachmentService] = None
) -> EmailAttachmentInvoiceProcessor:
    """Get EmailAttachmentInvoiceProcessor instance (singleton pattern)"""
    global _email_attachment_processor_instance
    if _email_attachment_processor_instance is None:
        _email_attachment_processor_instance = EmailAttachmentInvoiceProcessor(attachment_service)
    return _email_attachment_processor_instance
